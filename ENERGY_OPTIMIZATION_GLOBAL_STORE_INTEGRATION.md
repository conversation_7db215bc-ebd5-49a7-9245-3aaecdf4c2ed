# EnergyOptimization.vue 全局门店状态集成总结

## 概述

根据您的需求，我已经成功将EnergyOptimization.vue页面的本地门店选择替换为全局门店状态管理，借鉴了DeviceStatus.vue的实现方式，使用`globalStore.storeDisplayName`和`globalStore.selectedStoreId`来管理门店选择和数据获取。

## 主要修改

### 1. 模板部分修改

#### 页面头部区域
```vue
<!-- 修改前：本地门店选择器 -->
<div class="filter-area">
  <el-select v-model="selectedStore" placeholder="选择门店">
    <el-option
      v-for="store in storeList"
      :key="store.id"
      :label="store.name"
      :value="store.id"
    ></el-option>
  </el-select>
</div>

<!-- 修改后：全局状态显示 -->
<div class="filter-area">
  <div class="current-selection">
    <el-tag size="large" type="info">
      <el-icon><Shop /></el-icon>
      {{ globalStore.storeDisplayName }}
    </el-tag>
    <el-tag size="large" type="success" style="margin-left: 10px">
      <el-icon><Calendar /></el-icon>
      {{ globalStore.selectedDate }}
    </el-tag>
  </div>
  <div class="selection-hint">
    <el-text type="info" size="small">
      请在Dashboard页面选择门店和日期
    </el-text>
  </div>
</div>
```

### 2. 脚本部分修改

#### 导入和状态管理
```javascript
// 修改前
import { ref, onMounted, watch } from "vue";
import { getEnergyOptimizationData, getStoreList } from "@/api";
import { ElMessage } from 'element-plus';

const selectedStore = ref("");
const storeList = ref([]);

// 修改后
import { ref, onMounted, watch } from "vue";
import { getEnergyOptimizationData } from "@/api";
import { ElMessage } from 'element-plus';
import { useGlobalStore } from "@/stores/global";
import { Shop, Calendar } from '@element-plus/icons-vue';

const globalStore = useGlobalStore();
```

#### 数据获取方法
```javascript
// 修改前
const fetchEnergyOptimizationData = async () => {
  if (!selectedStore.value) return;
  
  try {
    const data = await getEnergyOptimizationData({
      storeId: selectedStore.value,
      date: new Date().toISOString().split('T')[0],
      timeRange: "day"
    });
    energyData.value = data;
  } catch (error) {
    console.error("获取能耗优化数据失败:", error);
    ElMessage.error("获取能耗优化数据失败");
  }
};

// 修改后
const fetchEnergyOptimizationData = async () => {
  if (!globalStore.selectedStoreId) return;
  
  try {
    console.log(
      `能耗优化页面：获取门店 ${globalStore.selectedStoreId} 在 ${globalStore.selectedDate} 的数据`
    );
    const data = await getEnergyOptimizationData({
      storeId: globalStore.selectedStoreId,
      date: globalStore.selectedDate,
      timeRange: "day"
    });
    energyData.value = data;
    console.log("能耗优化数据获取成功:", data);
  } catch (error) {
    console.error("获取能耗优化数据失败:", error);
    ElMessage.error("获取能耗优化数据失败");
  }
};
```

#### 监听和生命周期
```javascript
// 修改前
watch(selectedStore, (newStoreId) => {
  if (newStoreId) {
    fetchEnergyOptimizationData();
  }
});

onMounted(async () => {
  try {
    const stores = await getStoreList();
    storeList.value = stores;
    if (stores.length > 0) {
      selectedStore.value = stores[0].id;
      await fetchEnergyOptimizationData();
    }
  } catch (error) {
    console.error("初始化数据失败:", error);
    ElMessage.error("初始化数据失败");
  }
});

// 修改后
watch(
  [() => globalStore.selectedStoreId, () => globalStore.selectedDate],
  async () => {
    console.log("能耗优化页面：检测到全局状态变化，重新获取数据");
    await fetchEnergyOptimizationData();
  }
);

onMounted(async () => {
  try {
    await fetchEnergyOptimizationData();
  } catch (error) {
    console.error("初始化数据失败:", error);
    ElMessage.error("初始化数据失败");
  }
});
```

### 3. 样式部分修改

#### 页面头部样式
```scss
// 修改前
.filter-area {
  display: flex;
  gap: 15px;
}

// 修改后
.filter-area {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;

  .current-selection {
    display: flex;
    align-items: center;
  }

  .selection-hint {
    opacity: 0.7;
  }
}
```

## 功能特性

### 1. 全局状态管理
- **统一的门店选择**: 所有页面共享同一个门店选择状态
- **自动同步**: 在Dashboard页面选择门店后，其他页面自动更新
- **状态持久化**: 门店选择状态在页面间切换时保持不变

### 2. 用户体验优化
- **直观的状态显示**: 使用标签显示当前选择的门店和日期
- **操作指引**: 提供明确的操作提示，引导用户在Dashboard页面进行选择
- **实时更新**: 全局状态变化时自动重新获取数据

### 3. 数据一致性
- **统一的数据源**: 所有页面使用相同的门店ID和日期参数
- **同步更新**: 确保所有页面显示的数据基于相同的查询条件
- **错误处理**: 统一的错误处理和用户提示

## 技术实现

### 1. 全局状态管理
使用Pinia的`useGlobalStore`来管理全局状态：
- `globalStore.selectedStoreId`: 当前选择的门店ID
- `globalStore.storeDisplayName`: 当前门店的显示名称
- `globalStore.selectedDate`: 当前选择的日期

### 2. 响应式监听
使用Vue的`watch`API监听全局状态变化：
```javascript
watch(
  [() => globalStore.selectedStoreId, () => globalStore.selectedDate],
  async () => {
    await fetchEnergyOptimizationData();
  }
);
```

### 3. 组件图标
引入Element Plus图标组件：
- `Shop`: 门店图标
- `Calendar`: 日期图标

## 与DeviceStatus.vue的一致性

### 1. 相同的页面头部结构
- 使用相同的标签样式显示门店和日期
- 相同的操作提示文案
- 一致的布局和样式

### 2. 相同的状态管理模式
- 使用相同的全局状态变量
- 相同的监听和更新逻辑
- 一致的错误处理方式

### 3. 相同的用户交互模式
- 统一的操作流程
- 一致的用户提示
- 相同的视觉反馈

## 业务价值

### 1. 用户体验统一
- 所有页面使用相同的门店选择方式
- 减少用户的学习成本
- 提供一致的操作体验

### 2. 数据一致性保证
- 避免不同页面显示不同门店的数据
- 确保数据查询条件的统一性
- 减少数据不一致的问题

### 3. 维护成本降低
- 统一的状态管理减少代码重复
- 集中的门店选择逻辑便于维护
- 一致的实现模式便于扩展

## 后续优化建议

### 1. 状态持久化
- 将门店选择状态保存到localStorage
- 页面刷新后恢复之前的选择
- 支持用户偏好设置

### 2. 加载状态优化
- 添加数据加载的loading状态
- 提供更好的加载体验
- 支持数据刷新操作

### 3. 错误处理增强
- 更详细的错误信息提示
- 支持重试机制
- 提供降级方案

### 4. 性能优化
- 避免重复的API请求
- 实现数据缓存机制
- 优化页面切换性能

---

**总结**: 本次修改成功将EnergyOptimization.vue页面集成到全局门店状态管理系统中，与DeviceStatus.vue保持了一致的用户体验和技术实现，提供了统一的门店选择和数据管理方式。
