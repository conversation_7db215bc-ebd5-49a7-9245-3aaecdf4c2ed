#!/bin/bash

# 调试订单数据 - 检查具体的订单状态和时间分布

echo "=== 调试订单数据分析 ==="

# API端点
ORDER_ANALYSIS_API="http://localhost:8081/api/v1/pos/dashboard/order-analysis"

# 测试数据
STORE_ID="1001"
DATE="2025-01-16"

echo "门店: $STORE_ID"
echo "日期: $DATE"
echo ""

# 构建请求体
REQUEST_BODY='{
  "storeId": "'$STORE_ID'",
  "date": "'$DATE'",
  "timeRange": "today"
}'

echo "=== 获取Order Analysis数据 ==="
RESPONSE=$(curl -s -X POST "$ORDER_ANALYSIS_API" \
  -H "Content-Type: application/json" \
  -d "$REQUEST_BODY")

echo "=== 分析各时段数据 ==="

# 检查有订单的时段
echo "09:00时段:"
echo "$RESPONSE" | jq '.body.todayOrders[9]'

echo ""
echo "14:00时段:"
echo "$RESPONSE" | jq '.body.todayOrders[14]'

echo ""
echo "15:00时段:"
echo "$RESPONSE" | jq '.body.todayOrders[15]'

echo ""
echo "17:00时段:"
echo "$RESPONSE" | jq '.body.todayOrders[17]'

echo ""
echo "=== 计算预期的已完成订单数 ==="

# 提取各时段数据
HOUR_09_TOTAL=$(echo "$RESPONSE" | jq '.body.todayOrders[9].total')
HOUR_09_CANCELED=$(echo "$RESPONSE" | jq '.body.todayOrders[9].canceled')
HOUR_09_COMPLETED=$((HOUR_09_TOTAL - HOUR_09_CANCELED))

HOUR_14_TOTAL=$(echo "$RESPONSE" | jq '.body.todayOrders[14].total')
HOUR_14_CANCELED=$(echo "$RESPONSE" | jq '.body.todayOrders[14].canceled')
HOUR_14_COMPLETED=$((HOUR_14_TOTAL - HOUR_14_CANCELED))

HOUR_15_TOTAL=$(echo "$RESPONSE" | jq '.body.todayOrders[15].total')
HOUR_15_CANCELED=$(echo "$RESPONSE" | jq '.body.todayOrders[15].canceled')
HOUR_15_COMPLETED=$((HOUR_15_TOTAL - HOUR_15_CANCELED))

HOUR_17_TOTAL=$(echo "$RESPONSE" | jq '.body.todayOrders[17].total')
HOUR_17_CANCELED=$(echo "$RESPONSE" | jq '.body.todayOrders[17].canceled')
HOUR_17_COMPLETED=$((HOUR_17_TOTAL - HOUR_17_CANCELED))

echo "09:00 - 总数: $HOUR_09_TOTAL, 取消: $HOUR_09_CANCELED, 预期已完成: $HOUR_09_COMPLETED"
echo "14:00 - 总数: $HOUR_14_TOTAL, 取消: $HOUR_14_CANCELED, 预期已完成: $HOUR_14_COMPLETED"
echo "15:00 - 总数: $HOUR_15_TOTAL, 取消: $HOUR_15_CANCELED, 预期已完成: $HOUR_15_COMPLETED"
echo "17:00 - 总数: $HOUR_17_TOTAL, 取消: $HOUR_17_CANCELED, 预期已完成: $HOUR_17_COMPLETED"

echo ""
echo "=== 现在测试Dashboard API ==="

DASHBOARD_API="http://localhost:8081/api/v1/pos/dashboard/data"
DASHBOARD_REQUEST='{
  "storeId": "'$STORE_ID'",
  "date": "'$DATE'"
}'

DASHBOARD_RESPONSE=$(curl -s -X POST "$DASHBOARD_API" \
  -H "Content-Type: application/json" \
  -d "$DASHBOARD_REQUEST")

echo "Dashboard API各时段数据:"
echo "09:00:" $(echo "$DASHBOARD_RESPONSE" | jq '.body.hourlyOrderTrend[9]')
echo "14:00:" $(echo "$DASHBOARD_RESPONSE" | jq '.body.hourlyOrderTrend[14]')
echo "15:00:" $(echo "$DASHBOARD_RESPONSE" | jq '.body.hourlyOrderTrend[15]')
echo "17:00:" $(echo "$DASHBOARD_RESPONSE" | jq '.body.hourlyOrderTrend[17]')

echo ""
echo "=== 数据一致性检查 ==="
DASH_09=$(echo "$DASHBOARD_RESPONSE" | jq '.body.hourlyOrderTrend[9].manual')
DASH_14=$(echo "$DASHBOARD_RESPONSE" | jq '.body.hourlyOrderTrend[14].manual')
DASH_15=$(echo "$DASHBOARD_RESPONSE" | jq '.body.hourlyOrderTrend[15].manual')
DASH_17=$(echo "$DASHBOARD_RESPONSE" | jq '.body.hourlyOrderTrend[17].manual')

echo "Dashboard实际 vs 预期已完成:"
echo "09:00: $DASH_09 vs $HOUR_09_COMPLETED $([ "$DASH_09" -eq "$HOUR_09_COMPLETED" ] && echo "✅" || echo "❌")"
echo "14:00: $DASH_14 vs $HOUR_14_COMPLETED $([ "$DASH_14" -eq "$HOUR_14_COMPLETED" ] && echo "✅" || echo "❌")"
echo "15:00: $DASH_15 vs $HOUR_15_COMPLETED $([ "$DASH_15" -eq "$HOUR_15_COMPLETED" ] && echo "✅" || echo "❌")"
echo "17:00: $DASH_17 vs $HOUR_17_COMPLETED $([ "$DASH_17" -eq "$HOUR_17_COMPLETED" ] && echo "✅" || echo "❌")"

echo ""
echo "调试完成！"
