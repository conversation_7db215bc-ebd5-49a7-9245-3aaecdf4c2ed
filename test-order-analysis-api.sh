#!/bin/bash

# 测试订单分析API的自助POS会员订单功能

echo "=== 测试订单分析API - 自助POS会员订单统计 ==="

# API端点
API_URL="http://localhost:8081/pos/dashboard/order-analysis"

# 测试数据
STORE_ID="1001"
DATE="2025-01-16"

# 构建请求体
REQUEST_BODY='{
  "storeId": "'$STORE_ID'",
  "date": "'$DATE'",
  "timeRange": "today"
}'

echo "请求URL: $API_URL"
echo "请求体: $REQUEST_BODY"
echo ""

# 发送请求
echo "发送请求..."
RESPONSE=$(curl -s -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -d "$REQUEST_BODY")

echo "响应结果:"
echo "$RESPONSE" | jq '.'

echo ""
echo "=== 检查自助POS会员订单统计数据 ==="

# 提取自助POS会员订单统计数据
MEMBER_STATS=$(echo "$RESPONSE" | jq '.body.selfServiceMemberStats')
echo "自助POS会员订单统计:"
echo "$MEMBER_STATS" | jq '.'

# 提取今日订单分时段数据中的selfServiceMember字段
echo ""
echo "=== 检查分时段数据中的会员订单字段 ==="
HOURLY_DATA=$(echo "$RESPONSE" | jq '.body.todayOrders[12]') # 检查12点的数据
echo "12:00时段数据:"
echo "$HOURLY_DATA" | jq '.'

echo ""
echo "测试完成！"
