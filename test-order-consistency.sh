#!/bin/bash

# 测试两个订单接口的数据一致性

echo "=== 测试订单接口数据一致性 ==="

# API端点
DASHBOARD_API="http://localhost:8081/api/v1/pos/dashboard/data"
ORDER_ANALYSIS_API="http://localhost:8081/api/v1/pos/dashboard/order-analysis"

# 测试数据
STORE_ID="1001"
DATE="2025-01-16"

echo "测试门店: $STORE_ID"
echo "测试日期: $DATE"
echo ""

# 构建请求体
DASHBOARD_REQUEST='{
  "storeId": "'$STORE_ID'",
  "date": "'$DATE'"
}'

ORDER_ANALYSIS_REQUEST='{
  "storeId": "'$STORE_ID'",
  "date": "'$DATE'",
  "timeRange": "today"
}'

echo "=== 1. 测试 Dashboard API ==="
echo "请求: $DASHBOARD_API"
DASHBOARD_RESPONSE=$(curl -s -X POST "$DASHBOARD_API" \
  -H "Content-Type: application/json" \
  -d "$DASHBOARD_REQUEST")

echo "Dashboard API 17:00时段数据:"
echo "$DASHBOARD_RESPONSE" | jq '.body.hourlyOrderTrend[17]'

echo ""
echo "=== 2. 测试 Order Analysis API ==="
echo "请求: $ORDER_ANALYSIS_API"
ORDER_ANALYSIS_RESPONSE=$(curl -s -X POST "$ORDER_ANALYSIS_API" \
  -H "Content-Type: application/json" \
  -d "$ORDER_ANALYSIS_REQUEST")

echo "Order Analysis API 17:00时段数据:"
echo "$ORDER_ANALYSIS_RESPONSE" | jq '.body.todayOrders[17]'

echo ""
echo "=== 3. 数据对比分析 ==="

# 提取17:00时段的数据进行对比
DASHBOARD_17_MANUAL=$(echo "$DASHBOARD_RESPONSE" | jq '.body.hourlyOrderTrend[17].manual')
DASHBOARD_17_SELF=$(echo "$DASHBOARD_RESPONSE" | jq '.body.hourlyOrderTrend[17].selfService')

ORDER_17_MANUAL=$(echo "$ORDER_ANALYSIS_RESPONSE" | jq '.body.todayOrders[17].manual')
ORDER_17_SELF=$(echo "$ORDER_ANALYSIS_RESPONSE" | jq '.body.todayOrders[17].selfService')
ORDER_17_TOTAL=$(echo "$ORDER_ANALYSIS_RESPONSE" | jq '.body.todayOrders[17].total')
ORDER_17_CANCELED=$(echo "$ORDER_ANALYSIS_RESPONSE" | jq '.body.todayOrders[17].canceled')

echo "Dashboard API (只统计已完成订单):"
echo "  Manual: $DASHBOARD_17_MANUAL"
echo "  SelfService: $DASHBOARD_17_SELF"

echo ""
echo "Order Analysis API (统计所有订单):"
echo "  Total: $ORDER_17_TOTAL"
echo "  Manual: $ORDER_17_MANUAL"
echo "  SelfService: $ORDER_17_SELF"
echo "  Canceled: $ORDER_17_CANCELED"

echo ""
echo "=== 4. 一致性验证 ==="

# 计算预期的已完成订单数
EXPECTED_COMPLETED_MANUAL=$((ORDER_17_MANUAL - ORDER_17_CANCELED))
EXPECTED_COMPLETED_SELF=$ORDER_17_SELF

echo "预期已完成订单数:"
echo "  Manual: $EXPECTED_COMPLETED_MANUAL (总数 $ORDER_17_MANUAL - 取消 $ORDER_17_CANCELED)"
echo "  SelfService: $EXPECTED_COMPLETED_SELF"

echo ""
if [ "$DASHBOARD_17_MANUAL" -eq "$EXPECTED_COMPLETED_MANUAL" ] && [ "$DASHBOARD_17_SELF" -eq "$EXPECTED_COMPLETED_SELF" ]; then
    echo "✅ 数据一致性验证通过！"
else
    echo "❌ 数据不一致！"
    echo "Dashboard Manual: $DASHBOARD_17_MANUAL, 预期: $EXPECTED_COMPLETED_MANUAL"
    echo "Dashboard SelfService: $DASHBOARD_17_SELF, 预期: $EXPECTED_COMPLETED_SELF"
fi

echo ""
echo "测试完成！"
