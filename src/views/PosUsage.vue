<template>
  <div class="pos-usage-container">
    <div class="page-header">
      <div class="left-section">
        <h1>POS使用率分析</h1>
        <RegionSelector
          @store-selected="handleStoreSelected"
          :initial-store-id="globalStore.selectedStoreId"
        />
      </div>
      <div class="right-section">
        <el-date-picker
          v-model="currentDate"
          type="date"
          placeholder="选择日期"
          :disabled-date="disabledDate"
          @change="handleDateChange"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </div>
    </div>

    <!-- 总体使用率卡片 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card shadow="hover" class="usage-card">
          <template #header>
            <div class="card-header">
              <span>人工POS使用率</span>
            </div>
          </template>
          <div class="usage-info">
            <div class="usage-value">
              {{ Number(currentStoreData.manual?.usageRate || 0).toFixed(2) }}%
            </div>
            <div class="usage-progress">
              <el-progress
                :percentage="currentStoreData.manual?.usageRate || 0"
                :stroke-width="18"
                :show-text="false"
                color="#409EFF"
              />
            </div>
            <div class="usage-details">
              <div class="detail-item">
                <div class="detail-label">总运行时长</div>
                <div class="detail-value">
                  {{
                    (currentStoreData.manual?.totalHours || 0).toFixed(2)
                  }}小时
                </div>
              </div>
              <div class="detail-item">
                <div class="detail-label">有效使用时长</div>
                <div class="detail-value">
                  {{
                    (currentStoreData.manual?.activeHours || 0).toFixed(2)
                  }}小时
                </div>
              </div>
              <div class="detail-item">
                <div class="detail-label">闲置时长</div>
                <div class="detail-value">
                  {{
                    (
                      (currentStoreData.manual?.totalHours || 0) -
                      (currentStoreData.manual?.activeHours || 0)
                    ).toFixed(2)
                  }}小时
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="usage-card">
          <template #header>
            <div class="card-header">
              <span>自助POS使用率</span>
            </div>
          </template>
          <div class="usage-info">
            <div class="usage-value">
              {{ (currentStoreData.selfService?.usageRate || 0).toFixed(2) }}%
            </div>
            <div class="usage-progress">
              <el-progress
                :percentage="currentStoreData.selfService?.usageRate || 0"
                :stroke-width="18"
                :show-text="false"
                color="#67C23A"
              />
            </div>
            <div class="usage-details">
              <div class="detail-item">
                <div class="detail-label">总运行时长</div>
                <div class="detail-value">
                  {{
                    (currentStoreData.selfService?.totalHours || 0).toFixed(2)
                  }}小时
                </div>
              </div>
              <div class="detail-item">
                <div class="detail-label">有效使用时长</div>
                <div class="detail-value">
                  {{
                    (currentStoreData.selfService?.activeHours || 0).toFixed(2)
                  }}小时
                </div>
              </div>
              <div class="detail-item">
                <div class="detail-label">闲置时长</div>
                <div class="detail-value">
                  {{
                    (
                      (currentStoreData.selfService?.totalHours || 0) -
                      (currentStoreData.selfService?.activeHours || 0)
                    ).toFixed(2)
                  }}小时
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 各时段使用率图表 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>各时段使用率分布</span>
              <div class="header-actions">
                <el-radio-group v-model="usageViewType" size="small">
                  <el-radio-button label="percentage">百分比</el-radio-button>
                  <el-radio-button label="devices">设备数</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <HourlyUsageChart
              :chart-data="currentStoreData.hourlyUsage || []"
              :view-type="usageViewType"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 各设备使用情况表格 -->
    <el-row :gutter="20" class="table-row">
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>人工POS设备使用详情</span>
              <el-tag
                size="small"
                :type="getTagType(currentStoreData.manual?.usageRate || 0)"
                >{{
                  getUsageLevel(currentStoreData.manual?.usageRate || 0)
                }}</el-tag
              >
            </div>
          </template>
          <el-table
            :data="currentStoreData.manual?.devices || []"
            style="width: 100%"
          >
            <el-table-column prop="deviceId" label="机台号" width="100" />
            <el-table-column prop="usageRate" label="使用率">
              <template #default="scope">
                <div class="usage-rate-cell">
                  {{ scope.row.usageRate.toFixed(2) }}%
                  <el-progress
                    :percentage="scope.row.usageRate"
                    :stroke-width="6"
                    :status="getProgressStatus(scope.row.usageRate)"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="activeHours" label="有效使用时长">
              <template #default="scope">
                {{ scope.row.activeHours.toFixed(2) }}小时
              </template>
            </el-table-column>
            <el-table-column prop="idleHours" label="闲置时长">
              <template #default="scope">
                {{ scope.row.idleHours.toFixed(2) }}小时
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>自助POS设备使用详情</span>
              <el-tag
                size="small"
                :type="getTagType(currentStoreData.selfService?.usageRate || 0)"
                >{{
                  getUsageLevel(currentStoreData.selfService?.usageRate || 0)
                }}</el-tag
              >
            </div>
          </template>
          <el-table
            :data="currentStoreData.selfService?.devices || []"
            style="width: 100%"
          >
            <el-table-column prop="deviceId" label="机台号" width="100" />
            <el-table-column prop="usageRate" label="使用率">
              <template #default="scope">
                <div class="usage-rate-cell">
                  {{ scope.row.usageRate.toFixed(2) }}%
                  <el-progress
                    :percentage="scope.row.usageRate"
                    :stroke-width="6"
                    :status="getProgressStatus(scope.row.usageRate)"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="activeHours" label="有效使用时长">
              <template #default="scope">
                {{ scope.row.activeHours.toFixed(2) }}小时
              </template>
            </el-table-column>
            <el-table-column prop="idleHours" label="闲置时长">
              <template #default="scope">
                {{ scope.row.idleHours.toFixed(2) }}小时
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 优化建议卡片 -->
    <el-row :gutter="20" class="suggestion-row">
      <el-col :span="24">
        <el-card shadow="hover" class="suggestion-card">
          <template #header>
            <div class="card-header">
              <span>优化建议</span>
            </div>
          </template>
          <div v-if="getSuggestions().length > 0">
            <el-alert
              v-for="(suggestion, index) in getSuggestions()"
              :key="index"
              :title="suggestion.title"
              :type="suggestion.type"
              :description="suggestion.description"
              show-icon
              :closable="false"
              style="margin-bottom: 10px"
            />
          </div>
          <div v-else class="no-suggestions">
            <el-empty description="暂无优化建议" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { getPosUsageData } from "@/api";
import HourlyUsageChart from "@/components/charts/HourlyUsageChart.vue";
import RegionSelector from "@/components/RegionSelector.vue";
import { useGlobalStore } from "@/stores/global";

// 使用全局状态管理
const globalStore = useGlobalStore();

// 本地状态
const posUsageData = ref([]);
const usageViewType = ref("percentage");

// 使用全局状态的响应式引用
const currentDate = ref(globalStore.selectedDate);
const currentStoreId = ref(globalStore.selectedStoreId);

// 计算当前选中门店的数据
const currentStoreData = computed(() => {
  if (!globalStore.selectedStoreId || posUsageData.value.length === 0) {
    return {};
  }

  const storeData = posUsageData.value.find(
    (item) => item.storeId === globalStore.selectedStoreId
  );
  return storeData || {};
});

// 方法
const getProgressStatus = (rate) => {
  if (rate >= 60) return "success";
  if (rate >= 40) return "";
  return "exception";
};

const getTagType = (rate) => {
  if (rate >= 60) return "success";
  if (rate >= 40) return "warning";
  return "danger";
};

const getUsageLevel = (rate) => {
  if (rate >= 60) return "高效";
  if (rate >= 40) return "中效";
  return "低效";
};

const getSuggestions = () => {
  const suggestions = [];

  if (!currentStoreData.value || !currentStoreData.value.manual) {
    return suggestions;
  }

  // 人工POS使用率低的建议
  if (currentStoreData.value.manual.usageRate < 40) {
    suggestions.push({
      title: "人工POS使用率过低",
      type: "warning",
      description: `建议减少${Math.ceil(
        (currentStoreData.value.manual.devices?.length || 0) * 0.2
      )}台人工POS设备，并优化收银员工作安排，提高设备利用率。`,
    });
  }

  // 自助POS使用率低的建议
  if (currentStoreData.value.selfService.usageRate < 35) {
    suggestions.push({
      title: "自助POS使用率过低",
      type: "warning",
      description: `建议减少${Math.ceil(
        (currentStoreData.value.selfService.devices?.length || 0) * 0.25
      )}台自助POS设备，并加强引导宣传，提高顾客自助结算率。`,
    });
  }

  // 闲置时间长的设备建议
  const idleManualDevices = (
    currentStoreData.value.manual.devices || []
  ).filter((d) => d.idleHours > 7);
  if (idleManualDevices.length > 0) {
    suggestions.push({
      title: "人工POS闲置时间过长",
      type: "info",
      description: `有${idleManualDevices.length}台人工POS设备闲置时间超过7小时，建议在非高峰时段关闭部分设备以节约能耗。`,
    });
  }

  // 增加设备建议
  if (currentStoreData.value.manual.usageRate > 65) {
    suggestions.push({
      title: "人工POS负载较高",
      type: "success",
      description:
        "当前人工POS使用率较高，如果出现排队现象，建议适当增加1-2台设备。",
    });
  }

  return suggestions;
};

// 日期限制函数：不能选择7天前和今天之后的日期
const disabledDate = (time) => {
  const today = new Date();
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(today.getDate() - 7);

  // 禁用7天前的日期和今天之后的日期
  return (
    time.getTime() < sevenDaysAgo.getTime() || time.getTime() > today.getTime()
  );
};

// 处理日期变化
const handleDateChange = async (newDate) => {
  if (newDate) {
    console.log(`日期变更为: ${newDate}`);
    // 更新全局状态
    globalStore.updateSelectedDate(newDate);
    currentDate.value = newDate;
    // 刷新数据
    await fetchPosUsageData();
  }
};

// 处理门店选择
const handleStoreSelected = async (selection) => {
  console.log("Selected store:", selection);

  if (selection && selection.storeInfo) {
    const { storeInfo } = selection;
    console.log(`选择的门店信息:`, {
      区域: storeInfo.pgName,
      子区域: storeInfo.subArea,
      门店: `${storeInfo.storeId}-${storeInfo.storeName}`,
    });

    // 更新全局状态
    globalStore.updateSelectedStore(storeInfo);
    currentStoreId.value = storeInfo.storeId;

    // 刷新数据
    await fetchPosUsageData();
  }
};

// 获取数据的函数
const fetchPosUsageData = async () => {
  if (!globalStore.selectedStoreId) return;

  try {
    console.log(
      `POS使用率页面：获取门店 ${globalStore.selectedStoreId} 在 ${globalStore.selectedDate} 的数据`
    );
    const data = await getPosUsageData({
      storeId: globalStore.selectedStoreId,
      date: globalStore.selectedDate,
    });
    posUsageData.value = data;
    console.log("POS使用率数据获取成功:", data);
  } catch (error) {
    console.error("获取POS使用率数据失败:", error);
  }
};

// 生命周期
onMounted(async () => {
  try {
    // 同步全局状态到本地状态
    currentStoreId.value = globalStore.selectedStoreId;
    currentDate.value = globalStore.selectedDate;

    // 获取初始数据
    await fetchPosUsageData();
  } catch (error) {
    console.error("初始化数据失败:", error);
  }
});

// 监听全局状态变化，重新获取数据
watch(
  [() => globalStore.selectedStoreId, () => globalStore.selectedDate],
  async () => {
    console.log("POS使用率页面：检测到全局状态变化，重新获取数据");
    await fetchPosUsageData();
  }
);
</script>

<style lang="scss" scoped>
.pos-usage-container {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .left-section {
      display: flex;
      align-items: center;
      gap: 20px;

      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }

    .right-section {
      display: flex;
      align-items: center;
    }
  }

  .usage-card {
    margin-bottom: 20px;

    .usage-info {
      display: flex;
      flex-direction: column;
      align-items: center;

      .usage-value {
        font-size: 36px;
        font-weight: 500;
        margin-bottom: 20px;
      }

      .usage-progress {
        width: 90%;
        margin-bottom: 30px;
      }

      .usage-details {
        display: flex;
        width: 100%;
        justify-content: space-around;

        .detail-item {
          text-align: center;

          .detail-label {
            color: #909399;
            font-size: 14px;
            margin-bottom: 5px;
          }

          .detail-value {
            font-size: 18px;
            font-weight: 500;
          }
        }
      }
    }
  }

  .chart-row,
  .table-row,
  .suggestion-row {
    margin-top: 20px;
  }

  .chart-container {
    height: 350px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .usage-rate-cell {
    width: 200px;
  }

  .no-suggestions {
    padding: 20px;
  }
}
</style>
