<template>
  <div class="energy-optimization-container">
    <div class="page-header">
      <div class="left-section">
        <h1>能耗优化建议</h1>
        <RegionSelector
          @store-selected="handleStoreSelected"
          :initial-store-id="globalStore.selectedStoreId"
        />
      </div>
      <div class="right-section">
        <el-date-picker
          v-model="currentDate"
          type="date"
          placeholder="选择日期"
          :disabled-date="disabledDate"
          @change="handleDateChange"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </div>
    </div>

    <!-- 能耗概览卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">总能耗 (今日)</div>
          <div class="card-value">
            {{ energyData.dailyConsumption.toFixed(2) }} kWh
          </div>
          <div class="card-footer">
            较昨日:
            <span :class="energyData.dailyChange < 0 ? 'down' : 'up'">
              {{ energyData.dailyChange > 0 ? "+" : ""
              }}{{ energyData.dailyChange.toFixed(2) }}%
            </span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">本周总能耗</div>
          <div class="card-value">
            {{ energyData.weeklyConsumption.toFixed(2) }} kWh
          </div>
          <div class="card-footer">
            较上周:
            <span :class="energyData.weeklyChange < 0 ? 'down' : 'up'">
              {{ energyData.weeklyChange > 0 ? "+" : ""
              }}{{ energyData.weeklyChange.toFixed(2) }}%
            </span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">优化后预计能耗</div>
          <div class="card-value">
            {{ energyData.projectedConsumption.toFixed(2) }} kWh
          </div>
          <div class="card-footer">
            预计节约:
            <span class="down">
              -{{ energyData.savingPercentage.toFixed(2) }}%
            </span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-title">年度节约预估</div>
          <div class="card-value">
            ¥{{ (energyData.yearlySavingCost / 10000).toFixed(2) }}万
          </div>
          <div class="card-footer">
            节电量: {{ (energyData.yearlySavingEnergy / 10000).toFixed(2) }}万度
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 优化建议卡片 -->
    <!-- <el-card shadow="hover" class="optimization-card">
      <template #header>
        <div class="card-header">
          <span>能耗优化建议</span>
        </div>
      </template>
      <el-table :data="energyData.optimizationSuggestions" style="width: 100%">
        <el-table-column type="expand">
          <template #default="props">
            <div class="suggestion-detail">
              <p>{{ props.row.description }}</p>
              <div class="detail-metrics">
                <div class="metric">
                  <div class="metric-label">实施难度</div>
                  <el-rate
                    v-model="props.row.difficulty"
                    disabled
                    text-color="#ff9900"
                  />
                </div>
                <div class="metric">
                  <div class="metric-label">投资回报周期</div>
                  <span>{{ props.row.roi }} 个月</span>
                </div>
                <div class="metric">
                  <div class="metric-label">预计投资</div>
                  <span>¥{{ props.row.investment.toLocaleString() }}</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="优化措施" />
        <el-table-column prop="type" label="类型">
          <template #default="scope">
            <el-tag :type="getSuggestionTagType(scope.row.type)">
              {{ scope.row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="potentialSaving" label="潜在节约">
          <template #default="scope">
            {{ scope.row.potentialSaving }} kWh/天
          </template>
        </el-table-column>
        <el-table-column prop="costSaving" label="每年节约成本">
          <template #default="scope">
            ¥{{ scope.row.costSaving.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button
              size="small"
              type="success"
              @click="handleImplement(scope.row)"
            >
              实施
            </el-button>
            <el-button size="small" @click="handleDetail(scope.row)"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card> -->

    <!-- 历史节约能耗曲线图 -->
    <el-card shadow="hover" class="energy-saving-trend-card">
      <template #header>
        <div class="card-header">
          <span>历史节约能耗趋势</span>
          <div class="header-actions">
            <el-radio-group v-model="savingTrendTimeRange" size="small">
              <el-radio-button label="week">最近一周</el-radio-button>
              <el-radio-button label="month">最近一月</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      <div class="chart-container">
        <EnergySavingTrendChart :chart-data="energySavingTrendData" />
      </div>
    </el-card>

    <!-- 设备耗电量排行 -->
    <el-card shadow="hover" class="ranking-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <span>设备耗电量排行</span>
            <div class="one-click-energy-saving">
              <el-switch
                v-model="allEnergySavingEnabled"
                @change="handleOneClickEnergySaving"
                active-text="一键节能"
                inactive-text="一键节能"
                size="large"
              />
              <el-text type="info" size="small" style="margin-left: 8px">
                {{
                  allEnergySavingEnabled
                    ? "已开启所有设备节能模式"
                    : "点击开启所有设备节能模式"
                }}
              </el-text>
            </div>
          </div>
          <div class="header-actions">
            <el-radio-group v-model="rankingTimeRange" size="small">
              <el-radio-button label="day">今日</el-radio-button>
              <el-radio-button label="week">本周</el-radio-button>
              <!-- <el-radio-button label="month">本月</el-radio-button> -->
            </el-radio-group>
          </div>
        </div>
      </template>
      <el-table :data="energyData.deviceConsumptionRanking" style="width: 100%">
        <el-table-column prop="rank" label="排名" width="80" />
        <el-table-column prop="deviceId" label="机台号" width="120" />
        <el-table-column prop="type" label="设备类型">
          <template #default="scope">
            {{ scope.row.type === "manual" ? "人工POS" : "自助POS" }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="location" label="位置" /> -->
        <el-table-column prop="consumption" label="耗电量">
          <template #default="scope">
            {{ scope.row.consumption.toFixed(2) }} kWh
          </template>
        </el-table-column>
        <el-table-column prop="usageRate" label="使用率">
          <template #default="scope">
            <div class="usage-rate-cell">
              {{ scope.row.usageRate.toFixed(2) }}%
              <el-progress
                :percentage="scope.row.usageRate.toFixed(2)"
                :stroke-width="6"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="optimizationEnabled" label="节能模式是否开启">
          <template #default="scope">
            <el-switch
              v-model="scope.row.optimizationEnabled"
              @change="handleOptimizationToggle(scope.row)"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { getEnergyOptimizationData } from "@/api";
import { ElMessage } from "element-plus";
import RegionSelector from "@/components/RegionSelector.vue";
import EnergySavingTrendChart from "@/components/charts/EnergySavingTrendChart.vue";
import { useGlobalStore } from "@/stores/global";

// 使用全局状态管理
const globalStore = useGlobalStore();

// 本地状态
const rankingTimeRange = ref("day");
const savingTrendTimeRange = ref("week"); // 节约能耗趋势时间范围
const allEnergySavingEnabled = ref(false); // 一键节能开关状态

// 使用全局状态的响应式引用
const currentDate = ref(globalStore.selectedDate);
const currentStoreId = ref(globalStore.selectedStoreId);
const energyData = ref({
  dailyConsumption: 0,
  dailyChange: 0,
  weeklyConsumption: 0,
  weeklyChange: 0,
  projectedConsumption: 0,
  savingPercentage: 0,
  yearlySavingEnergy: 0,
  yearlySavingCost: 0,
  optimizationSuggestions: [],
  deviceConsumptionRanking: [],
});

// 历史节约能耗趋势数据 (Mock数据)
const energySavingTrendData = ref([]);

// 方法
// const getSuggestionTagType = (type) => {
//   const types = {
//     硬件更新: "danger",
//     运营优化: "warning",
//     软件调整: "success",
//     使用习惯: "info",
//   };
//   return types[type] || "info";
// };

const handleOptimizationToggle = (device) => {
  console.log(
    `设备 ${device.deviceId} 节能优化状态切换为: ${device.optimizationEnabled}`
  );
  ElMessage.success(
    `设备 ${device.deviceId} 节能优化已${
      device.optimizationEnabled ? "开启" : "关闭"
    }`
  );
  // 这里可以调用API保存设备的优化状态

  // 检查是否所有设备都开启了节能模式
  updateAllEnergySavingStatus();
};

// 一键节能处理
const handleOneClickEnergySaving = (enabled) => {
  console.log(`一键节能${enabled ? "开启" : "关闭"}`);

  // 更新所有设备的节能状态
  if (energyData.value.deviceConsumptionRanking) {
    energyData.value.deviceConsumptionRanking.forEach((device) => {
      device.optimizationEnabled = enabled;
    });
  }

  ElMessage.success(`已${enabled ? "开启" : "关闭"}所有设备的节能模式`);
  // 这里可以调用API批量更新所有设备的节能状态
};

// 更新一键节能开关状态
const updateAllEnergySavingStatus = () => {
  if (
    !energyData.value.deviceConsumptionRanking ||
    energyData.value.deviceConsumptionRanking.length === 0
  ) {
    allEnergySavingEnabled.value = false;
    return;
  }

  // 检查是否所有设备都开启了节能模式
  const allEnabled = energyData.value.deviceConsumptionRanking.every(
    (device) => device.optimizationEnabled
  );
  allEnergySavingEnabled.value = allEnabled;
};

// const handleImplement = (suggestion) => {
//   console.log("实施优化建议", suggestion);
//   // 实现实施优化建议的逻辑
// };

// const handleDetail = (suggestion) => {
//   console.log("查看优化建议详情", suggestion);
//   // 实现查看优化建议详情的逻辑
// };

// 日期限制函数：不能选择7天前和今天之后的日期
const disabledDate = (time) => {
  const today = new Date();
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(today.getDate() - 7);

  // 禁用7天前的日期和今天之后的日期
  return (
    time.getTime() < sevenDaysAgo.getTime() || time.getTime() > today.getTime()
  );
};

// 处理日期变化
const handleDateChange = async (newDate) => {
  if (newDate) {
    console.log(`日期变更为: ${newDate}`);
    // 更新全局状态
    globalStore.updateSelectedDate(newDate);
    currentDate.value = newDate;
    // 刷新数据
    await fetchEnergyOptimizationData();
  }
};

// 处理门店选择
const handleStoreSelected = async (selection) => {
  console.log("Selected store:", selection);

  if (selection && selection.storeInfo) {
    const { storeInfo } = selection;
    console.log(`选择的门店信息:`, {
      区域: storeInfo.pgName,
      子区域: storeInfo.subArea,
      门店: `${storeInfo.storeId}-${storeInfo.storeName}`,
    });

    // 更新全局状态
    globalStore.updateSelectedStore(storeInfo);
    currentStoreId.value = storeInfo.storeId;

    // 刷新数据
    await fetchEnergyOptimizationData();
  }
};

// 生成历史节约能耗趋势Mock数据
const generateEnergySavingTrendData = (timeRange) => {
  const data = [];
  const days = timeRange === "week" ? 7 : 30;
  const today = new Date();

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(today.getDate() - i);

    // 生成随机的节约能耗数据
    const baseSaving = 15 + Math.random() * 10; // 基础节约15-25 kWh
    const dailySaving =
      Math.round((baseSaving + Math.sin(i * 0.5) * 5) * 100) / 100;

    data.push({
      date: date.toISOString().split("T")[0],
      dailySaving: dailySaving,
      cumulativeSaving:
        data.length > 0
          ? Math.round(
              (data[data.length - 1].cumulativeSaving + dailySaving) * 100
            ) / 100
          : dailySaving,
    });
  }

  return data;
};

// 获取能耗优化数据
const fetchEnergyOptimizationData = async () => {
  if (!globalStore.selectedStoreId) return;

  try {
    console.log(
      `能耗优化页面：获取门店 ${globalStore.selectedStoreId} 在 ${globalStore.selectedDate} 的数据`
    );
    const data = await getEnergyOptimizationData({
      storeId: globalStore.selectedStoreId,
      date: globalStore.selectedDate,
      timeRange: "day",
    });
    energyData.value = data;

    // 生成历史节约能耗趋势数据
    energySavingTrendData.value = generateEnergySavingTrendData(
      savingTrendTimeRange.value
    );

    // 更新一键节能开关状态
    updateAllEnergySavingStatus();

    console.log("能耗优化数据获取成功:", data);
  } catch (error) {
    console.error("获取能耗优化数据失败:", error);
    ElMessage.error("获取能耗优化数据失败");
  }
};

// 生命周期
onMounted(async () => {
  try {
    // 同步全局状态到本地状态
    currentStoreId.value = globalStore.selectedStoreId;
    currentDate.value = globalStore.selectedDate;

    // 获取初始数据
    await fetchEnergyOptimizationData();
  } catch (error) {
    console.error("初始化数据失败:", error);
    ElMessage.error("初始化数据失败");
  }
});

// 监听全局状态变化，重新获取数据
watch(
  [() => globalStore.selectedStoreId, () => globalStore.selectedDate],
  async () => {
    console.log("能耗优化页面：检测到全局状态变化，重新获取数据");
    await fetchEnergyOptimizationData();
  }
);

// 监听节约能耗趋势时间范围变化
watch(savingTrendTimeRange, (newTimeRange) => {
  console.log(`节约能耗趋势时间范围变更为: ${newTimeRange}`);
  energySavingTrendData.value = generateEnergySavingTrendData(newTimeRange);
});
</script>

<style lang="scss" scoped>
.energy-optimization-container {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .left-section {
      display: flex;
      align-items: center;
      gap: 20px;

      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }

    .right-section {
      display: flex;
      align-items: center;
    }
  }

  .overview-card {
    height: 180px;

    .card-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 10px;
    }

    .card-value {
      font-size: 28px;
      font-weight: 500;
      margin-bottom: 20px;
    }

    .card-footer {
      font-size: 14px;

      .up {
        color: #f56c6c;
      }

      .down {
        color: #67c23a;
      }
    }
  }

  .optimization-card,
  .ranking-card,
  .energy-saving-trend-card {
    margin-top: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;
      gap: 20px;

      .one-click-energy-saving {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background-color: #f5f7fa;
        border-radius: 6px;
        border: 1px solid #e4e7ed;

        .el-switch {
          margin-right: 8px;
        }
      }
    }
  }

  .chart-container {
    height: 350px;
  }

  .suggestion-detail {
    padding: 20px;
    background-color: #f5f7fa;

    .detail-metrics {
      display: flex;
      margin-top: 15px;
      gap: 40px;

      .metric {
        display: flex;
        flex-direction: column;

        .metric-label {
          font-size: 14px;
          color: #909399;
          margin-bottom: 5px;
        }
      }
    }
  }

  .usage-rate-cell {
    width: 200px;
  }
}
</style>
