<template>
  <div class="device-status-container">
    <div class="page-header">
      <div class="left-section">
        <h1>设备状态监控</h1>
        <RegionSelector
          @store-selected="handleStoreSelected"
          :initial-store-id="globalStore.selectedStoreId"
        />
      </div>
      <div class="right-section">
        <el-date-picker
          v-model="currentDate"
          type="date"
          placeholder="选择日期"
          :disabled-date="disabledDate"
          @change="handleDateChange"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </div>
    </div>

    <!-- 设备状态概览 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="status-card">
          <div class="card-title">总设备数</div>
          <div class="card-value">{{ deviceData.totalDevices }}</div>
          <div class="card-footer">
            <el-tag size="small">人工POS: {{ deviceData.manualCount }}</el-tag>
            <el-tag size="small" type="success"
              >自助POS: {{ deviceData.selfServiceCount }}</el-tag
            >
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="status-card">
          <div class="card-title">在线设备</div>
          <div class="card-value">{{ deviceData.onlineDevices }}</div>
          <div class="card-footer">
            <el-progress
              :percentage="
                Math.round(
                  (deviceData.onlineDevices / deviceData.totalDevices) * 100
                )
              "
              :stroke-width="6"
              status="success"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="status-card">
          <div class="card-title">工作中设备</div>
          <div class="card-value">{{ deviceData.activeDevices }}</div>
          <div class="card-footer">
            <el-progress
              :percentage="
                Math.round(
                  (deviceData.activeDevices / deviceData.onlineDevices) * 100
                )
              "
              :stroke-width="6"
              color="#409EFF"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="status-card">
          <div class="card-title">空闲设备</div>
          <div class="card-value">{{ deviceData.abnormalDevices }}</div>
          <div class="card-footer">
            <el-tag
              size="small"
              type="danger"
              v-if="deviceData.abnormalDevices > 0"
            >
              需要关注
            </el-tag>
            <el-tag size="small" type="success" v-else> 一切正常 </el-tag>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 设备状态列表 -->
    <el-card shadow="hover" class="device-table-card">
      <template #header>
        <div class="card-header">
          <span>设备状态列表</span>
          <div class="header-actions">
            <el-radio-group v-model="deviceTypeFilter" size="small">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="manual">人工POS</el-radio-button>
              <el-radio-button label="selfService">自助POS</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      <el-table :data="filteredDevices" style="width: 100%">
        <el-table-column prop="deviceId" label="机台号" width="120" />
        <el-table-column prop="type" label="设备类型">
          <template #default="scope">
            {{ scope.row.type === "manual" ? "人工POS" : "自助POS" }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="location" label="位置" /> -->
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastActiveTime" label="最后活跃时间" />
        <el-table-column prop="runningTime" label="今日运行时长" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="handleDetail(scope.row)"
              >详情</el-button
            >
            <el-button
              size="small"
              type="primary"
              @click="handleRemoteControl(scope.row)"
              :disabled="scope.row.status === 'offline'"
            >
              远程控制
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 设备详情弹框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="设备详情"
      width="800px"
      :before-close="handleDetailClose"
    >
      <div class="device-detail-content">
        <div class="device-info">
          <h3>设备信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="机台号">
              {{ selectedDevice?.deviceId }}
            </el-descriptions-item>
            <el-descriptions-item label="设备类型">
              {{ selectedDevice?.type === "manual" ? "人工POS" : "自助POS" }}
            </el-descriptions-item>
            <el-descriptions-item label="当前状态">
              <el-tag :type="getStatusTagType(selectedDevice?.status)">
                {{ getStatusText(selectedDevice?.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="最后活跃时间">
              {{ selectedDevice?.lastActiveTime }}
            </el-descriptions-item>
            <el-descriptions-item label="今日运行时长">
              {{ selectedDevice?.runningTime }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="operation-logs">
          <h3>远程控制操作日志</h3>
          <el-table :data="operationLogs" style="width: 100%" max-height="300">
            <el-table-column prop="operatorName" label="操作人" width="120" />
            <el-table-column
              prop="operationTime"
              label="操作时间"
              width="180"
            />
            <el-table-column prop="operationType" label="操作类型" width="120">
              <template #default="scope">
                <el-tag :type="getOperationTagType(scope.row.operationType)">
                  {{ getOperationText(scope.row.operationType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="result" label="执行结果" width="100">
              <template #default="scope">
                <el-tag
                  :type="scope.row.result === 'success' ? 'success' : 'danger'"
                >
                  {{ scope.row.result === "success" ? "成功" : "失败" }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" />
          </el-table>
          <div v-if="operationLogs.length === 0" class="empty-logs">
            <el-empty description="暂无操作记录" />
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 远程控制弹框 -->
    <el-dialog
      v-model="remoteControlDialogVisible"
      title="远程控制"
      width="500px"
      :before-close="handleRemoteControlClose"
    >
      <div class="remote-control-content">
        <div class="device-info-brief">
          <el-alert
            :title="`正在控制设备：${selectedDevice?.deviceId} (${
              selectedDevice?.type === 'manual' ? '人工POS' : '自助POS'
            })`"
            type="info"
            :closable="false"
            show-icon
          />
        </div>

        <div class="control-options">
          <h4>请选择控制操作：</h4>
          <el-radio-group v-model="selectedOperation" size="large">
            <el-radio label="shutdown" border>
              <el-icon><SwitchButton /></el-icon>
              关机
            </el-radio>
            <el-radio label="logout" border>
              <el-icon><User /></el-icon>
              下机
            </el-radio>
          </el-radio-group>
        </div>

        <div class="operation-description" v-if="selectedOperation">
          <el-alert
            :title="getOperationDescription(selectedOperation)"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>

        <div class="operation-remark">
          <el-form-item label="操作备注">
            <el-input
              v-model="operationRemark"
              type="textarea"
              :rows="3"
              placeholder="请输入操作备注（可选）"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleRemoteControlClose">取消</el-button>
          <el-button
            type="primary"
            @click="executeRemoteControl"
            :disabled="!selectedOperation"
            :loading="executing"
          >
            {{ executing ? "执行中..." : "确认执行" }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import {
  getDeviceStatusData,
  getDeviceOperationLogs,
  executeDeviceControl,
} from "@/api";
import RegionSelector from "@/components/RegionSelector.vue";
import { useGlobalStore } from "@/stores/global";
import { ElMessage, ElMessageBox } from "element-plus";
import { SwitchButton, User } from "@element-plus/icons-vue";

// 使用全局状态管理
const globalStore = useGlobalStore();

// 本地状态
const deviceData = ref({
  totalDevices: 0,
  onlineDevices: 0,
  activeDevices: 0,
  abnormalDevices: 0,
  manualCount: 0,
  selfServiceCount: 0,
  devices: [],
});
const deviceTypeFilter = ref("all");

// 使用全局状态的响应式引用
const currentDate = ref(globalStore.selectedDate);
const currentStoreId = ref(globalStore.selectedStoreId);

// 弹框相关状态
const detailDialogVisible = ref(false);
const remoteControlDialogVisible = ref(false);
const selectedDevice = ref(null);
const selectedOperation = ref("");
const operationRemark = ref("");
const executing = ref(false);

// 操作日志数据
const operationLogs = ref([]);

// 计算属性
const filteredDevices = computed(() => {
  if (deviceTypeFilter.value === "all") {
    return deviceData.value.devices;
  }
  return deviceData.value.devices.filter(
    (device) => device.type === deviceTypeFilter.value
  );
});

// 方法
const getStatusTagType = (status) => {
  const types = {
    active: "success",
    idle: "info",
    offline: "danger",
    abnormal: "warning",
  };
  return types[status] || "info";
};

const getStatusText = (status) => {
  const texts = {
    active: "使用中",
    idle: "空闲",
    offline: "离线",
    abnormal: "异常",
  };
  return texts[status] || status;
};

const handleDetail = async (device) => {
  console.log("查看设备详情", device);
  selectedDevice.value = device;

  // 获取设备操作日志
  await fetchOperationLogs(device.deviceId);

  detailDialogVisible.value = true;
};

const handleRemoteControl = (device) => {
  console.log("远程控制设备", device);
  selectedDevice.value = device;
  selectedOperation.value = "";
  operationRemark.value = "";
  remoteControlDialogVisible.value = true;
};

// 获取设备操作日志
const fetchOperationLogs = async (deviceId) => {
  try {
    // 调用真实API获取操作日志
    const logs = await getDeviceOperationLogs({
      deviceId: deviceId,
      storeId: globalStore.selectedStoreId,
      startDate: globalStore.selectedDate,
      endDate: globalStore.selectedDate,
    });
    operationLogs.value = logs;
  } catch (error) {
    console.error("获取操作日志失败:", error);
    operationLogs.value = [];
  }
};

// 执行远程控制
const executeRemoteControl = async () => {
  if (!selectedOperation.value) {
    ElMessage.warning("请选择操作类型");
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确认要对设备 ${selectedDevice.value.deviceId} 执行${getOperationText(
        selectedOperation.value
      )}操作吗？`,
      "确认操作",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    executing.value = true;

    // 调用真实API执行远程控制
    await executeDeviceControl({
      deviceId: selectedDevice.value.deviceId,
      storeId: globalStore.selectedStoreId,
      operationType: selectedOperation.value,
      remark: operationRemark.value,
    });

    ElMessage.success(
      `${getOperationText(selectedOperation.value)}操作执行成功`
    );

    // 关闭弹框并刷新数据
    remoteControlDialogVisible.value = false;
    await fetchDeviceStatusData();
  } catch (error) {
    if (error !== "cancel") {
      console.error("远程控制操作失败:", error);
      ElMessage.error("操作执行失败，请重试");
    }
  } finally {
    executing.value = false;
  }
};

// 弹框关闭处理
const handleDetailClose = () => {
  detailDialogVisible.value = false;
  selectedDevice.value = null;
  operationLogs.value = [];
};

const handleRemoteControlClose = () => {
  remoteControlDialogVisible.value = false;
  selectedDevice.value = null;
  selectedOperation.value = "";
  operationRemark.value = "";
};

// 操作类型相关方法
const getOperationTagType = (operationType) => {
  const types = {
    shutdown: "danger",
    logout: "warning",
  };
  return types[operationType] || "info";
};

const getOperationText = (operationType) => {
  const texts = {
    shutdown: "关机",
    logout: "下机",
  };
  return texts[operationType] || operationType;
};

const getOperationDescription = (operationType) => {
  const descriptions = {
    shutdown: "关机操作将完全关闭设备电源，设备将无法继续提供服务",
    logout: "下机操作将登出当前用户，但设备仍保持开机状态",
  };
  return descriptions[operationType] || "";
};

// 日期限制函数：不能选择7天前和今天之后的日期
const disabledDate = (time) => {
  const today = new Date();
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(today.getDate() - 7);

  // 禁用7天前的日期和今天之后的日期
  return (
    time.getTime() < sevenDaysAgo.getTime() || time.getTime() > today.getTime()
  );
};

// 处理日期变化
const handleDateChange = async (newDate) => {
  if (newDate) {
    console.log(`日期变更为: ${newDate}`);
    // 更新全局状态
    globalStore.updateSelectedDate(newDate);
    currentDate.value = newDate;
    // 刷新数据
    await fetchDeviceStatusData();
  }
};

// 处理门店选择
const handleStoreSelected = async (selection) => {
  console.log("Selected store:", selection);

  if (selection && selection.storeInfo) {
    const { storeInfo } = selection;
    console.log(`选择的门店信息:`, {
      区域: storeInfo.pgName,
      子区域: storeInfo.subArea,
      门店: `${storeInfo.storeId}-${storeInfo.storeName}`,
    });

    // 更新全局状态
    globalStore.updateSelectedStore(storeInfo);
    currentStoreId.value = storeInfo.storeId;

    // 刷新数据
    await fetchDeviceStatusData();
  }
};

// 获取数据的函数
const fetchDeviceStatusData = async () => {
  if (!globalStore.selectedStoreId) return;

  try {
    console.log(
      `设备状态页面：获取门店 ${globalStore.selectedStoreId} 在 ${globalStore.selectedDate} 的数据`
    );
    const data = await getDeviceStatusData({
      storeId: globalStore.selectedStoreId,
      date: globalStore.selectedDate,
    });
    deviceData.value = data;
    console.log("设备状态数据获取成功:", data);
  } catch (error) {
    console.error("获取设备状态数据失败:", error);
  }
};

// 生命周期
onMounted(async () => {
  try {
    // 同步全局状态到本地状态
    currentStoreId.value = globalStore.selectedStoreId;
    currentDate.value = globalStore.selectedDate;

    // 获取初始数据
    await fetchDeviceStatusData();
  } catch (error) {
    console.error("初始化数据失败:", error);
  }
});

// 监听全局状态变化，重新获取数据
watch(
  [() => globalStore.selectedStoreId, () => globalStore.selectedDate],
  async () => {
    console.log("设备状态页面：检测到全局状态变化，重新获取数据");
    await fetchDeviceStatusData();
  }
);
</script>

<style lang="scss" scoped>
.device-status-container {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .left-section {
      display: flex;
      align-items: center;
      gap: 20px;

      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }

    .right-section {
      display: flex;
      align-items: center;
    }
  }

  .status-card {
    height: 180px;

    .card-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 10px;
    }

    .card-value {
      font-size: 28px;
      font-weight: 500;
      margin-bottom: 20px;
    }

    .card-footer {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }

  .device-table-card {
    margin-top: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

// 弹框样式
.device-detail-content {
  .device-info {
    margin-bottom: 30px;

    h3 {
      margin: 0 0 15px 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }

  .operation-logs {
    h3 {
      margin: 0 0 15px 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .empty-logs {
      padding: 20px 0;
    }
  }
}

.remote-control-content {
  .device-info-brief {
    margin-bottom: 20px;
  }

  .control-options {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 15px 0;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }

    :deep(.el-radio) {
      margin-right: 20px;
      margin-bottom: 10px;

      &.is-bordered {
        padding: 12px 20px;
        border-radius: 6px;

        .el-radio__label {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }
  }

  .operation-description {
    margin-bottom: 20px;
  }

  .operation-remark {
    :deep(.el-form-item__label) {
      font-weight: 500;
    }
  }
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px 20px;
}

:deep(.el-dialog__body) {
  padding: 10px 20px 20px 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px 20px;
}
</style>
