<template>
  <div class="account-container">
    <el-card class="account-card">
      <template #header>
        <div class="card-header">
          <h3>个人中心</h3>
        </div>
      </template>
      <el-form :model="userStore.userInfo" label-width="100px">
        <el-form-item label="工号">
          <el-input v-model="userStore.userInfo.work_id" disabled></el-input>
        </el-form-item>
        <el-form-item label="用户名">
          <el-input v-model="userStore.userInfo.emp_name" disabled></el-input>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="userStore.userInfo.mobile" disabled></el-input>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { reactive } from "vue";
import { useUserStore } from "@/stores/user";

const userStore = useUserStore();

const accountForm = reactive({
  username: "admin",
  name: "管理员",
  email: "<EMAIL>",
  phone: "***********",
});

const saveChanges = () => {
  // 实现保存逻辑
  ElMessage.success("保存成功");
};
</script>

<style scoped>
.account-container {
  padding: 20px;
}

.account-card {
  max-width: 600px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
