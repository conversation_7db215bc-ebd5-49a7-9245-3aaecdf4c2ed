import { defineStore } from "pinia";
import { ref, computed } from "vue";

export const useGlobalStore = defineStore("global", () => {
  // 当前选中的门店信息
  const selectedStoreInfo = ref(null);
  
  // 当前选中的门店ID
  const selectedStoreId = ref(localStorage.getItem("selectedStoreId") || "1001");
  
  // 当前选中的日期
  const selectedDate = ref(new Date().toISOString().split("T")[0]);
  
  // 计算属性：获取门店显示名称
  const storeDisplayName = computed(() => {
    if (selectedStoreInfo.value) {
      return `${selectedStoreInfo.value.storeId}-${selectedStoreInfo.value.storeName}`;
    }
    return selectedStoreId.value;
  });
  
  // 计算属性：获取完整的门店信息字符串
  const storeFullInfo = computed(() => {
    if (selectedStoreInfo.value) {
      return `${selectedStoreInfo.value.pgName} > ${selectedStoreInfo.value.subArea} > ${selectedStoreInfo.value.storeId}-${selectedStoreInfo.value.storeName}`;
    }
    return selectedStoreId.value;
  });
  
  // 更新选中的门店信息
  const updateSelectedStore = (storeInfo) => {
    if (storeInfo && storeInfo.storeId) {
      selectedStoreInfo.value = storeInfo;
      selectedStoreId.value = storeInfo.storeId;
      
      // 持久化存储
      localStorage.setItem("selectedStoreId", storeInfo.storeId);
      localStorage.setItem("selectedStoreInfo", JSON.stringify(storeInfo));
      
      console.log("全局状态：门店信息已更新", {
        区域: storeInfo.pgName,
        子区域: storeInfo.subArea,
        门店: `${storeInfo.storeId}-${storeInfo.storeName}`,
      });
    }
  };
  
  // 更新选中的日期
  const updateSelectedDate = (date) => {
    if (date) {
      // 确保日期格式为 YYYY-MM-DD
      const formattedDate = typeof date === 'string' ? date : date.toISOString().split("T")[0];
      selectedDate.value = formattedDate;
      
      // 持久化存储
      localStorage.setItem("selectedDate", formattedDate);
      
      console.log("全局状态：日期已更新", formattedDate);
    }
  };
  
  // 初始化状态（从localStorage恢复）
  const initializeState = () => {
    // 恢复门店信息
    const storedStoreInfo = localStorage.getItem("selectedStoreInfo");
    if (storedStoreInfo) {
      try {
        selectedStoreInfo.value = JSON.parse(storedStoreInfo);
      } catch (error) {
        console.error("解析存储的门店信息失败:", error);
      }
    }
    
    // 恢复日期信息
    const storedDate = localStorage.getItem("selectedDate");
    if (storedDate) {
      selectedDate.value = storedDate;
    }
    
    console.log("全局状态已初始化:", {
      门店ID: selectedStoreId.value,
      门店信息: selectedStoreInfo.value,
      日期: selectedDate.value,
    });
  };
  
  // 清除状态
  const clearState = () => {
    selectedStoreInfo.value = null;
    selectedStoreId.value = "1001";
    selectedDate.value = new Date().toISOString().split("T")[0];
    
    // 清除本地存储
    localStorage.removeItem("selectedStoreId");
    localStorage.removeItem("selectedStoreInfo");
    localStorage.removeItem("selectedDate");
    
    console.log("全局状态已清除");
  };
  
  return {
    // 状态
    selectedStoreInfo,
    selectedStoreId,
    selectedDate,
    
    // 计算属性
    storeDisplayName,
    storeFullInfo,
    
    // 方法
    updateSelectedStore,
    updateSelectedDate,
    initializeState,
    clearState,
  };
});
